package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"path/filepath"
	"strings"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/alauda/compliance-operator/pkg/controller/scan"
)

type ReportAPIServer struct {
	client        client.Client
	reportService *scan.ReportDownloadService
	namespace     string
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

type ScanListResponse struct {
	Scans []ScanInfo `json:"scans"`
}

type ScanInfo struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Profile   string `json:"profile"`
	Phase     string `json:"phase"`
	Result    string `json:"result"`
	ScanID    string `json:"scanId,omitempty"`
	Timestamp string `json:"timestamp,omitempty"`
}

func NewReportAPIServer(client client.Client, namespace string) *ReportAPIServer {
	return &ReportAPIServer{
		client:        client,
		reportService: scan.NewReportDownloadService(client, ""),
		namespace:     namespace,
	}
}

func (s *ReportAPIServer) handleListScans(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	ctx := context.Background()
	var scanList complianceapi.ScanList
	if err := s.client.List(ctx, &scanList, client.InNamespace(s.namespace)); err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "Failed to list scans", err.Error())
		return
	}

	var scans []ScanInfo
	for _, scan := range scanList.Items {
		scanInfo := ScanInfo{
			Name:      scan.Name,
			Namespace: scan.Namespace,
			Profile:   scan.Spec.Profile,
			Phase:     scan.Status.Phase,
			Result:    scan.Status.Result,
		}

		if scan.Status.LatestResult != nil {
			scanInfo.ScanID = scan.Status.LatestResult.ScanID
			scanInfo.Timestamp = scan.Status.LatestResult.Timestamp.Format("2006-01-02 15:04:05")
		}

		scans = append(scans, scanInfo)
	}

	response := ScanListResponse{Scans: scans}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *ReportAPIServer) handleDownloadReport(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract scan name from URL path /download/{scanName}
	path := strings.TrimPrefix(r.URL.Path, "/download/")
	scanName := strings.TrimSuffix(path, "/")

	if scanName == "" {
		s.writeErrorResponse(w, http.StatusBadRequest, "Scan name is required", "")
		return
	}

	ctx := context.Background()
	zipData, err := s.reportService.GetLatestReportZip(ctx, scanName, s.namespace)
	if err != nil {
		s.writeErrorResponse(w, http.StatusInternalServerError, "Failed to generate report", err.Error())
		return
	}

	// Set headers for zip download
	w.Header().Set("Content-Type", "application/zip")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s-report.zip", scanName))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(zipData)))

	// Write zip data
	w.Write(zipData)
}

func (s *ReportAPIServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": "2025-06-30T01:00:00Z",
		"service":   "compliance-report-api",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *ReportAPIServer) writeErrorResponse(w http.ResponseWriter, statusCode int, message, details string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := ErrorResponse{
		Error:   message,
		Message: details,
	}
	json.NewEncoder(w).Encode(response)
}

func main() {
	var (
		kubeconfig = flag.String("kubeconfig", filepath.Join(homedir.HomeDir(), ".kube", "config"), "path to kubeconfig file")
		namespace  = flag.String("namespace", "compliance-system", "namespace where scans are located")
		port       = flag.String("port", "8080", "port for HTTP server")
	)
	flag.Parse()

	// Build Kubernetes client
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		log.Fatalf("Failed to build config: %v", err)
	}

	// Create controller-runtime client
	scheme := runtime.NewScheme()
	if err := complianceapi.AddToScheme(scheme); err != nil {
		log.Fatalf("Failed to add compliance API to scheme: %v", err)
	}

	k8sClient, err := client.New(config, client.Options{Scheme: scheme})
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}

	// Create API server
	server := NewReportAPIServer(k8sClient, *namespace)

	// Setup routes
	http.HandleFunc("/scans", server.handleListScans)
	http.HandleFunc("/download/", server.handleDownloadReport)
	http.HandleFunc("/health", server.handleHealth)

	// Add a simple index page
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}

		html := `<!DOCTYPE html>
<html>
<head>
    <title>Compliance Report API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { color: #007acc; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🛡️ Compliance Report API</h1>
    <p>Unified report download service for compliance scans</p>
    
    <h2>Available Endpoints:</h2>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/scans</code><br>
        List all available scans with their status
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/download/{scanName}</code><br>
        Download latest report zip for a specific scan
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <code>/health</code><br>
        Health check endpoint
    </div>
    
    <h2>Examples:</h2>
    <ul>
        <li><a href="/scans">List all scans</a></li>
        <li><a href="/download/stig-k8s-v2r2-node-scan">Download STIG scan report</a></li>
        <li><a href="/health">Health check</a></li>
    </ul>
</body>
</html>`
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(html))
	})

	log.Printf("🚀 Compliance Report API Server starting on port %s", *port)
	log.Printf("📁 Serving reports from namespace: %s", *namespace)
	log.Printf("🌐 Available endpoints:")
	log.Printf("   GET  /scans           - List all scans")
	log.Printf("   GET  /download/{scan} - Download report zip")
	log.Printf("   GET  /health          - Health check")
	log.Printf("   GET  /                - API documentation")

	if err := http.ListenAndServe(":"+*port, nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"k8s.io/apimachinery/pkg/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/alauda/compliance-operator/pkg/controller/scan"
)

func main() {
	// Create a new flag set to avoid conflicts with imported packages
	fs := flag.NewFlagSet("report-downloader", flag.ExitOnError)

	var (
		kubeconfig = fs.String("kubeconfig", filepath.Join(homedir.HomeDir(), ".kube", "config"), "path to kubeconfig file")
		namespace  = fs.String("namespace", "compliance-system", "namespace where scan reports are stored")
		scanName   = fs.String("scan", "", "scan name to download report for")
		output     = fs.String("output", "", "output file path for the zip (default: <scan-name>-report.zip)")
	)
	fs.Parse(os.Args[1:])

	if *scanName == "" {
		fmt.Println("Usage: report-downloader -scan <scan-name> [-output <file>] [-namespace <namespace>]")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  # Download latest report for a scan")
		fmt.Println("  report-downloader -scan stig-k8s-v2r2-node-scan")
		fmt.Println("")
		fmt.Println("  # Download to specific file")
		fmt.Println("  report-downloader -scan stig-k8s-v2r2-node-scan -output my-report.zip")
		fmt.Println("")
		fmt.Println("  # Download from different namespace")
		fmt.Println("  report-downloader -scan my-scan -namespace my-namespace")
		os.Exit(1)
	}

	// Set default output filename
	if *output == "" {
		*output = fmt.Sprintf("%s-report.zip", *scanName)
	}

	// Build Kubernetes client
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		log.Fatalf("Failed to build config: %v", err)
	}

	// Create controller-runtime client with default scheme
	scheme := runtime.NewScheme()

	// Add default Kubernetes APIs
	_ = clientgoscheme.AddToScheme(scheme)

	if err := complianceapi.AddToScheme(scheme); err != nil {
		log.Fatalf("Failed to add compliance API to scheme: %v", err)
	}

	k8sClient, err := client.New(config, client.Options{Scheme: scheme})
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}

	// Create report download service
	reportService := scan.NewReportDownloadService(k8sClient, "")

	fmt.Printf("Downloading latest report for scan '%s' in namespace '%s'...\n", *scanName, *namespace)

	// Download the report zip
	ctx := context.Background()
	zipData, err := reportService.GetLatestReportZip(ctx, *scanName, *namespace)
	if err != nil {
		log.Fatalf("Failed to download report: %v", err)
	}

	// Write to file
	if err := os.WriteFile(*output, zipData, 0644); err != nil {
		log.Fatalf("Failed to write report to file %s: %v", *output, err)
	}

	fmt.Printf("✅ Report downloaded successfully: %s\n", *output)
	fmt.Printf("📊 Report size: %d bytes\n", len(zipData))

	// Show what's in the zip
	fmt.Println("\n📁 Report contents:")
	fmt.Println("   - metadata.txt (scan information)")
	fmt.Println("   - openscap-reports/ (OpenSCAP HTML reports, if applicable)")
	fmt.Println("   - shell-script-reports/ (Shell script HTML reports and results, if applicable)")
	fmt.Println("")
	fmt.Printf("💡 Extract with: unzip %s\n", *output)
}

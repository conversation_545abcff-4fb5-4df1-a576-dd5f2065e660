package scan

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"time"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// ReportDownloadService provides unified report download functionality
type ReportDownloadService struct {
	client                   client.Client
	openSCAPReportServiceURL string
}

// NewReportDownloadService creates a new report download service
func NewReportDownloadService(client client.Client, openSCAPReportServiceURL string) *ReportDownloadService {
	if openSCAPReportServiceURL == "" {
		openSCAPReportServiceURL = "http://openscap-report-service.compliance-system.svc.cluster.local:8080"
	}
	return &ReportDownloadService{
		client:                   client,
		openSCAPReportServiceURL: openSCAPReportServiceURL,
	}
}

// GetLatestReportZip returns a zip file containing the latest scan reports
func (rds *ReportDownloadService) GetLatestReportZip(ctx context.Context, scanName, namespace string) ([]byte, error) {
	// Get the scan resource to determine the latest scanID
	var scan complianceapi.Scan
	if err := rds.client.Get(ctx, client.ObjectKey{
		Name:      scanName,
		Namespace: namespace,
	}, &scan); err != nil {
		return nil, fmt.Errorf("failed to get scan %s: %v", scanName, err)
	}

	if scan.Status.LatestResult == nil || scan.Status.LatestResult.ScanID == "" {
		return nil, fmt.Errorf("no latest result found for scan %s", scanName)
	}

	scanID := scan.Status.LatestResult.ScanID

	// Determine scan type based on profile
	var profile complianceapi.Profile
	if err := rds.client.Get(ctx, client.ObjectKey{
		Name:      scan.Spec.Profile,
		Namespace: namespace,
	}, &profile); err != nil {
		return nil, fmt.Errorf("failed to get profile %s: %v", scan.Spec.Profile, err)
	}

	// Create zip buffer
	var zipBuffer bytes.Buffer
	zipWriter := zip.NewWriter(&zipBuffer)
	defer zipWriter.Close()

	// Check if this is an OpenSCAP scan or shell script scan
	if profile.Spec.DataStream != nil && profile.Spec.DataStream.ContentFile != "" {
		// OpenSCAP scan - get reports from openscap-report-service
		if err := rds.addOpenSCAPReportsToZip(ctx, zipWriter, scanID); err != nil {
			return nil, fmt.Errorf("failed to add OpenSCAP reports: %v", err)
		}
	} else {
		// Shell script scan - get reports from ConfigMaps
		if err := rds.addConfigMapReportsToZip(ctx, zipWriter, scanName, scanID, namespace); err != nil {
			return nil, fmt.Errorf("failed to add ConfigMap reports: %v", err)
		}
	}

	// Add scan metadata
	if err := rds.addScanMetadataToZip(ctx, zipWriter, &scan); err != nil {
		return nil, fmt.Errorf("failed to add scan metadata: %v", err)
	}

	if err := zipWriter.Close(); err != nil {
		return nil, fmt.Errorf("failed to close zip writer: %v", err)
	}

	return zipBuffer.Bytes(), nil
}

// addOpenSCAPReportsToZip adds OpenSCAP reports from the report service to the zip
func (rds *ReportDownloadService) addOpenSCAPReportsToZip(ctx context.Context, zipWriter *zip.Writer, scanID string) error {
	// Try to download zip from openscap-report-service
	downloadURL := fmt.Sprintf("%s/download/%s", rds.openSCAPReportServiceURL, scanID)

	resp, err := http.Get(downloadURL)
	if err != nil {
		return fmt.Errorf("failed to download from openscap-report-service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("openscap-report-service returned status %d", resp.StatusCode)
	}

	// Read the zip content from the response
	zipContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read zip content: %v", err)
	}

	// Extract files from the downloaded zip and add them to our zip
	zipReader, err := zip.NewReader(bytes.NewReader(zipContent), int64(len(zipContent)))
	if err != nil {
		return fmt.Errorf("failed to read downloaded zip: %v", err)
	}

	for _, file := range zipReader.File {
		// Create a new file in our zip
		writer, err := zipWriter.Create("openscap-reports/" + file.Name)
		if err != nil {
			return fmt.Errorf("failed to create zip entry: %v", err)
		}

		// Open the file from the downloaded zip
		reader, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file from downloaded zip: %v", err)
		}

		// Copy content
		if _, err := io.Copy(writer, reader); err != nil {
			reader.Close()
			return fmt.Errorf("failed to copy file content: %v", err)
		}
		reader.Close()
	}

	return nil
}

// addConfigMapReportsToZip adds shell script scan reports from ConfigMaps to the zip
func (rds *ReportDownloadService) addConfigMapReportsToZip(ctx context.Context, zipWriter *zip.Writer, scanName, scanID, namespace string) error {
	// Get report ConfigMap
	reportConfigMapName := generateReportConfigMapName(scanID)
	var reportConfigMap corev1.ConfigMap
	if err := rds.client.Get(ctx, client.ObjectKey{
		Name:      reportConfigMapName,
		Namespace: namespace,
	}, &reportConfigMap); err != nil {
		return fmt.Errorf("failed to get report ConfigMap %s: %v", reportConfigMapName, err)
	}

	// Add HTML report
	if htmlContent, exists := reportConfigMap.Data["report.html"]; exists {
		writer, err := zipWriter.Create("shell-script-reports/report.html")
		if err != nil {
			return fmt.Errorf("failed to create HTML report entry: %v", err)
		}
		if _, err := writer.Write([]byte(htmlContent)); err != nil {
			return fmt.Errorf("failed to write HTML report: %v", err)
		}
	}

	// Get all result ConfigMaps for this scanID
	var configMaps corev1.ConfigMapList
	if err := rds.client.List(ctx, &configMaps, client.InNamespace(namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scanName,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return fmt.Errorf("failed to list result ConfigMaps: %v", err)
	}

	// Add individual result files
	for _, cm := range configMaps.Items {
		// Skip the main report ConfigMap
		if cm.Name == reportConfigMapName {
			continue
		}

		// Add result data
		if resultData, exists := cm.Data["result"]; exists {
			fileName := fmt.Sprintf("shell-script-reports/results/%s.xml", cm.Name)
			writer, err := zipWriter.Create(fileName)
			if err != nil {
				return fmt.Errorf("failed to create result entry: %v", err)
			}
			if _, err := writer.Write([]byte(resultData)); err != nil {
				return fmt.Errorf("failed to write result data: %v", err)
			}
		}

		// Add HTML content if it exists and is base64 encoded
		if htmlContent, exists := cm.Data["html_content"]; exists && htmlContent != "" {
			// Try to decode base64 content
			if decodedHTML, err := base64.StdEncoding.DecodeString(htmlContent); err == nil {
				fileName := fmt.Sprintf("shell-script-reports/html/%s.html", cm.Name)
				writer, err := zipWriter.Create(fileName)
				if err != nil {
					return fmt.Errorf("failed to create HTML entry: %v", err)
				}
				if _, err := writer.Write(decodedHTML); err != nil {
					return fmt.Errorf("failed to write HTML content: %v", err)
				}
			}
		}
	}

	return nil
}

// addScanMetadataToZip adds scan metadata to the zip
func (rds *ReportDownloadService) addScanMetadataToZip(ctx context.Context, zipWriter *zip.Writer, scan *complianceapi.Scan) error {
	metadata := fmt.Sprintf(`Scan Report Metadata
===================

Scan Name: %s
Namespace: %s
Profile: %s
Scan ID: %s
Phase: %s
Result: %s

Latest Result:
- Timestamp: %s
- Check Result: %s
- Report: %s

Statistics:
- Total: %d
- Pass: %d
- Fail: %d
- Error: %d
- Manual: %d
- Not Applicable: %d
- Inconsistent: %d

Generated: %s
`,
		scan.Name,
		scan.Namespace,
		scan.Spec.Profile,
		scan.Status.LatestResult.ScanID,
		scan.Status.Phase,
		scan.Status.Result,
		scan.Status.LatestResult.Timestamp.Format(time.RFC3339),
		scan.Status.LatestResult.CheckResultName,
		scan.Status.LatestResult.ReportName,
		scan.Status.LatestResult.Stats.Total,
		scan.Status.LatestResult.Stats.Pass,
		scan.Status.LatestResult.Stats.Fail,
		scan.Status.LatestResult.Stats.Error,
		scan.Status.LatestResult.Stats.Manual,
		scan.Status.LatestResult.Stats.NotApplicable,
		scan.Status.LatestResult.Stats.Inconsistent,
		time.Now().Format(time.RFC3339),
	)

	writer, err := zipWriter.Create("metadata.txt")
	if err != nil {
		return fmt.Errorf("failed to create metadata entry: %v", err)
	}

	if _, err := writer.Write([]byte(metadata)); err != nil {
		return fmt.Errorf("failed to write metadata: %v", err)
	}

	return nil
}

package scan

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"k8s.io/apimachinery/pkg/api/resource"
)

// 确保 rand 已初始化
func init() {
	rand.Seed(time.Now().UnixNano())
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateScanID 生成唯一的扫描ID，包含时间戳和随机字符串
func generateScanID(scan *complianceapi.Scan) string {
	// 计算 profile 的哈希值
	profileHash := fmt.Sprintf("%x", md5.Sum([]byte(scan.Spec.Profile)))

	// 使用当前时间生成时间戳
	timestamp := time.Now().UTC().Format("20060102-150405")

	// 添加短随机字符串确保唯一性
	randomSuffix := generateRandomString(4)

	return fmt.Sprintf("%s-%s-%s-%s",
		scan.Name,
		profileHash[:8],
		timestamp,
		randomSuffix)
}

// generateCheckResultName 根据 scanID 生成 CheckResult 资源名称
func generateCheckResultName(scanID string) string {
	return fmt.Sprintf("checkresult-%s", scanID)
}

// generateReportConfigMapName 根据 scanID 生成报告 ConfigMap 名称
func generateReportConfigMapName(scanID string) string {
	return fmt.Sprintf("report-%s", scanID)
}

// checkScanProgress monitors the progress of running scans
func (r *ScanReconciler) checkScanProgress(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	// 获取当前的 scanID
	scanID := ""
	if scan.Annotations != nil {
		scanID = scan.Annotations["compliance-operator.alauda.io/current-scan-id"]
	}

	if scanID == "" {
		log.Error(nil, "No scanID found in scan annotations")
		return r.updateScanStatus(ctx, scan, "Error", "Error", "No scanID found for running scan")
	}

	log.Info("Checking scan progress", "scanID", scanID)

	// Get all jobs for this scan with the current scanID
	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return ctrl.Result{}, err
	}

	totalJobs := len(jobs.Items)

	// If no jobs found, check if scan already has results (jobs may have been cleaned up)
	if totalJobs == 0 {
		// Check if we already have CheckResult for this scanID
		var existingResults complianceapi.CheckResultList
		if err := r.List(ctx, &existingResults, client.InNamespace(scan.Namespace),
			client.MatchingLabels{"compliance-operator.alauda.io/scan-id": scanID}); err != nil {
			log.Error(err, "Failed to check existing CheckResults")
			return ctrl.Result{RequeueAfter: 30 * time.Second}, nil
		}

		if len(existingResults.Items) > 0 {
			// Scan already completed and results exist, jobs were cleaned up
			log.Info("Scan already completed with results, jobs cleaned up", "checkResults", len(existingResults.Items))

			// Calculate final result from existing CheckResults
			aggregatedResult, err := r.calculateAggregatedResult(ctx, scan, scanID)
			if err != nil {
				log.Error(err, "Failed to calculate final aggregated results")
				return r.updateScanStatus(ctx, scan, "Error", "Error", fmt.Sprintf("Failed to calculate final results: %v", err))
			}

			// Update scan status to Done if not already
			return r.updateScanStatusWithEndTime(ctx, scan, "Done", aggregatedResult.Result, aggregatedResult.Message)
		}

		// No CheckResult for current scanID, check if scan has historical results and end time
		if scan.Status.EndTime != nil && len(scan.Status.HistoricalResults) > 0 {
			// Scan has completed before but status wasn't updated properly
			log.Info("Scan has historical results and end time, updating status to Done",
				"endTime", scan.Status.EndTime, "historicalResults", len(scan.Status.HistoricalResults))

			// Use the latest result from historical results
			latestResult := scan.Status.LatestResult
			if latestResult != nil {
				// Determine result based on stats
				result := "COMPLIANT"
				if latestResult.Stats.Fail > 0 || latestResult.Stats.Error > 0 {
					result = "NON-COMPLIANT"
				} else if latestResult.Stats.Inconsistent > 0 {
					result = "INCONSISTENT"
				}
				return r.updateScanStatus(ctx, scan, "Done", result, "Scan completed with historical results")
			} else {
				return r.updateScanStatus(ctx, scan, "Done", "UNKNOWN", "Scan completed but no result details available")
			}
		}

		// No jobs and no results, jobs haven't been created yet or failed to create
		log.Info("No jobs found for scan, requeuing")
		return ctrl.Result{RequeueAfter: 30 * time.Second}, nil
	}

	completedJobs := 0
	failedJobs := 0

	for _, job := range jobs.Items {
		if job.Status.Succeeded > 0 {
			completedJobs++
		} else if job.Status.Failed > 0 {
			failedJobs++
		}
	}

	log.Info("Scan progress", "total", totalJobs, "completed", completedJobs, "failed", failedJobs)

	// Check if all jobs are complete
	if completedJobs+failedJobs == totalJobs {
		log.Info("All jobs completed, collecting results", "completed", completedJobs, "failed", failedJobs)

		// Collect results and create CheckResult objects
		if err := r.collectScanResults(ctx, scan, jobs.Items, scanID); err != nil {
			log.Error(err, "Failed to collect scan results")
			return r.updateScanStatus(ctx, scan, "Error", "Error", fmt.Sprintf("Failed to collect results: %v", err))
		}

		// Calculate aggregated results
		aggregatedResult, err := r.calculateAggregatedResult(ctx, scan, scanID)
		if err != nil {
			log.Error(err, "Failed to calculate aggregated results")
			return r.updateScanStatus(ctx, scan, "Error", "Error", fmt.Sprintf("Failed to calculate results: %v", err))
		}

		// Generate HTML report
		if err := r.generateScanReport(ctx, scan, scanID, aggregatedResult.Stats); err != nil {
			log.Error(err, "Failed to generate scan report")
			// Don't fail the scan if report generation fails, just log the error
		}

		log.Info("Scan processing completed", "result", aggregatedResult.Result, "stats", aggregatedResult.Stats)

		// 更新 scan 的 LatestResult 字段
		if err := r.updateScanHistory(ctx, scan, scanID, generateCheckResultName(scanID), generateReportConfigMapName(scanID), aggregatedResult.Stats); err != nil {
			log.Error(err, "Failed to update scan history")
			// 不因为历史记录更新失败而使整个扫描失败
		}

		// 清理扫描Job（在TTL生效之前主动清理，确保及时释放资源）
		if err := r.cleanupScanJobs(ctx, scan, scanID); err != nil {
			log.Error(err, "Failed to cleanup scan jobs")
			// 不因为清理失败而使整个扫描失败，TTL机制会作为备用清理方案
		}

		return r.updateScanStatusWithEndTime(ctx, scan, "Done", aggregatedResult.Result, aggregatedResult.Message)
	}

	// Requeue to check again
	return ctrl.Result{RequeueAfter: 30 * time.Second}, nil
}

// collectScanResults processes completed jobs and creates CheckResult objects
func (r *ScanReconciler) collectScanResults(ctx context.Context, scan *complianceapi.Scan, jobs []batchv1.Job, scanID string) error {
	r.Log.Info("Starting to collect scan results", "scan", scan.Name, "totalJobs", len(jobs))

	// 使用 scanID 生成唯一的 CheckResult 名称
	checkResultName := generateCheckResultName(scanID)

	// 创建或获取聚合结果资源
	var aggregatedResult complianceapi.CheckResult
	var isUpdate bool

	// 检查是否已存在该 scanID 的 CheckResult
	var existingResults complianceapi.CheckResultList
	if err := r.List(ctx, &existingResults, client.InNamespace(scan.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan-id": scanID}); err != nil {
		r.Log.Error(err, "Failed to list existing CheckResults", "scanID", scanID)
		return err
	}

	if len(existingResults.Items) > 0 {
		// 找到现有结果，使用它
		aggregatedResult = existingResults.Items[0]
		isUpdate = true
		r.Log.Info("Found existing aggregated CheckResult", "checkResult", aggregatedResult.Name, "scanID", scanID)
	} else {
		// 创建新的聚合结果
		timestampStr := time.Now().UTC().Format("20060102-150405")
		aggregatedResult = complianceapi.CheckResult{
			ObjectMeta: metav1.ObjectMeta{
				Name:      checkResultName,
				Namespace: scan.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/scan":          scan.Name,
					"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
					"compliance-operator.alauda.io/scan-id":       scanID,
					"compliance-operator.alauda.io/resource-type": "checkresult",
					"compliance-operator.alauda.io/timestamp":     timestampStr,
				},
			},
			Spec: complianceapi.CheckResultSpec{
				ScanName:    scan.Name,
				ProfileName: scan.Spec.Profile,
				Timestamp:   metav1.Now(),
				RuleResults: []complianceapi.RuleResult{},
			},
		}

		// 设置所有者引用
		if err := controllerutil.SetControllerReference(scan, &aggregatedResult, r.Scheme); err != nil {
			r.Log.Error(err, "Failed to set controller reference for aggregated CheckResult")
			return err
		}
	}

	// 跟踪已处理的规则，避免重复处理
	processedRules := make(map[string]bool)
	for _, ruleResult := range aggregatedResult.Spec.RuleResults {
		processedRules[ruleResult.RuleID] = true
	}

	// 跟踪已处理的 ConfigMap，用于后续清理
	processedConfigMaps := make(map[string]bool)

	// 处理每个 Job 的结果
	for _, job := range jobs {
		ruleName := job.Labels["compliance-operator.alauda.io/rule"]
		checkType := job.Labels["compliance-operator.alauda.io/scan-type"]

		// 获取规则信息
		var rule complianceapi.Rule
		ruleSeverity := "medium"

		if err := r.Get(ctx, types.NamespacedName{Name: ruleName, Namespace: scan.Namespace}, &rule); err != nil {
			r.Log.Error(err, "Failed to get rule information", "rule", ruleName)
			// 继续使用默认值
		} else {
			// 使用规则信息
			if rule.Spec.Severity != "" {
				ruleSeverity = rule.Spec.Severity
			}
		}

		// 从 ConfigMap 获取检查结果数据
		resultData, err := r.getCheckResultFromPod(ctx, &job)
		if err != nil {
			r.Log.Error(err, "Failed to get check result from ConfigMap", "job", job.Name)
			continue
		}

		// 记录已处理的 ConfigMap
		configMapName := job.Name
		processedConfigMaps[configMapName] = true

		// 确定结果状态
		status := complianceapi.CheckResultStatusPass
		message := r.extractCheckMessage(resultData.Output, "passed")

		if resultData.ExitCode == 0 {
			// 退出码 0 = PASS
			status = complianceapi.CheckResultStatusPass
			message = r.extractCheckMessage(resultData.Output, "passed")
		} else if resultData.ExitCode == 1 {
			// 退出码 1 = FAIL
			status = complianceapi.CheckResultStatusFail
			message = r.extractCheckMessage(resultData.Output, "failed")
		} else if resultData.ExitCode == 2 {
			// 退出码 2 = MANUAL (需要人工分析)
			status = complianceapi.CheckResultStatusManual
			message = r.extractCheckMessage(resultData.Output, "manual")
		} else {
			// 其他退出码 = ERROR
			status = complianceapi.CheckResultStatusError
			message = r.extractCheckMessage(resultData.Output, "error")
		}

		// 获取节点名称（如果是节点扫描）
		nodeName := ""
		if checkType == "node" {
			nodeName = job.Labels["compliance-operator.alauda.io/node"]
		}

		// 更新或添加规则结果
		r.updateRuleResult(&aggregatedResult, ruleName, ruleSeverity, checkType, status, message, resultData.Output, nodeName)
	}

	// 计算聚合结果的统计数据
	stats := r.calculateStatsFromRuleResults(aggregatedResult.Spec.RuleResults)

	// 保存聚合结果
	if isUpdate {
		if err := r.Update(ctx, &aggregatedResult); err != nil {
			r.Log.Error(err, "Failed to update aggregated CheckResult", "checkResult", aggregatedResult.Name)
			return err
		}
		r.Log.Info("Successfully updated aggregated CheckResult", "checkResult", aggregatedResult.Name)
	} else {
		if err := r.Create(ctx, &aggregatedResult); err != nil {
			r.Log.Error(err, "Failed to create aggregated CheckResult", "checkResult", aggregatedResult.Name)
			return err
		}
		r.Log.Info("Successfully created aggregated CheckResult", "checkResult", aggregatedResult.Name)
	}

	// 生成报告
	reportConfigMapName := generateReportConfigMapName(scanID)
	if err := r.generateScanReport(ctx, scan, scanID, stats); err != nil {
		r.Log.Error(err, "Failed to generate scan report", "scan", scan.Name)
		// 不中断主流程
	}

	// 更新 Scan 状态，添加历史记录引用
	if err := r.updateScanHistory(ctx, scan, scanID, checkResultName, reportConfigMapName, stats); err != nil {
		r.Log.Error(err, "Failed to update scan history", "scan", scan.Name, "scanID", scanID)
		// 不中断主流程
	}

	// 清理已处理的 ConfigMap
	if err := r.cleanupProcessedConfigMaps(ctx, scan.Namespace, scanID, processedConfigMaps); err != nil {
		r.Log.Error(err, "Failed to cleanup processed ConfigMaps")
		// 不中断主流程
	}

	// 清理已处理的临时资源（只清理成功完成的Job的资源）
	if err := r.cleanupCompletedScanResources(ctx, scan, scanID, processedConfigMaps); err != nil {
		r.Log.Error(err, "Failed to cleanup temporary resources", "scan", scan.Name)
		// 不返回错误，因为清理失败不应该影响扫描状态
	}

	r.Log.Info("Scan processing completed", "scan", scan.Name, "scanID", scanID, "totalRules", len(aggregatedResult.Spec.RuleResults))
	return nil
}

// calculateStatsFromRuleResults calculates statistics from rule results
func (r *ScanReconciler) calculateStatsFromRuleResults(ruleResults []complianceapi.RuleResult) complianceapi.ScanStats {
	stats := complianceapi.ScanStats{
		Total: len(ruleResults),
	}

	for _, ruleResult := range ruleResults {
		switch ruleResult.Status {
		case complianceapi.CheckResultStatusPass:
			stats.Pass++
		case complianceapi.CheckResultStatusFail:
			stats.Fail++
		case complianceapi.CheckResultStatusError:
			stats.Error++
		case complianceapi.CheckResultStatusManual:
			stats.Manual++
		case complianceapi.CheckResultStatusInconsistent:
			stats.Inconsistent++
		case complianceapi.CheckResultStatusNotApplicable:
			stats.NotApplicable++
		}
	}

	return stats
}

// cleanupProcessedConfigMaps 清理已处理的 ConfigMap
func (r *ScanReconciler) cleanupProcessedConfigMaps(ctx context.Context, namespace string, scanID string, processedConfigMaps map[string]bool) error {
	r.Log.Info("Cleaning up processed ConfigMaps", "count", len(processedConfigMaps), "scanID", scanID)

	// 获取所有带有指定 scanID 和 temporary=true 标签的 ConfigMap
	var configMaps corev1.ConfigMapList
	if err := r.List(ctx, &configMaps, client.InNamespace(namespace),
		client.MatchingLabels{
			"compliance-operator.alauda.io/scan-id":   scanID,
			"compliance-operator.alauda.io/temporary": "true",
		}); err != nil {
		return err
	}

	// 只清理已处理的 ConfigMap
	for _, cm := range configMaps.Items {
		if processedConfigMaps[cm.Name] {
			if err := r.Delete(ctx, &cm); err != nil {
				if !errors.IsNotFound(err) {
					r.Log.Error(err, "Failed to delete ConfigMap", "configMap", cm.Name)
				}
			} else {
				r.Log.Info("Successfully deleted ConfigMap", "configMap", cm.Name)
			}
		}
	}

	return nil
}

// updateRuleResult 更新或添加规则结果到聚合结果中
func (r *ScanReconciler) updateRuleResult(
	aggregatedResult *complianceapi.CheckResult,
	ruleName, ruleSeverity, checkType, status, message, evidence, nodeName string) {

	// 查找现有规则结果
	var ruleResult *complianceapi.RuleResult
	for i := range aggregatedResult.Spec.RuleResults {
		if aggregatedResult.Spec.RuleResults[i].RuleID == ruleName {
			ruleResult = &aggregatedResult.Spec.RuleResults[i]
			break
		}
	}

	// 如果规则结果不存在，创建新的
	if ruleResult == nil {
		newRuleResult := complianceapi.RuleResult{
			RuleID:    ruleName,
			RuleName:  ruleName,
			Severity:  ruleSeverity,
			CheckType: checkType,
			Status:    status,
			Message:   message,
		}

		// 根据检查类型设置结果
		if checkType == "platform" {
			// Platform checks don't need additional setup
		} else if checkType == "node" && nodeName != "" {
			newRuleResult.NodeResults = []complianceapi.NodeResult{
				{
					NodeName: nodeName,
					Status:   status,
					Message:  message,
					Evidence: evidence,
				},
			}
		}

		aggregatedResult.Spec.RuleResults = append(aggregatedResult.Spec.RuleResults, newRuleResult)
	} else {
		// 更新现有规则结果
		if checkType == "platform" {
			ruleResult.Status = status
			ruleResult.Message = message
		} else if checkType == "node" && nodeName != "" {
			// 查找现有节点结果
			var nodeResult *complianceapi.NodeResult
			for i := range ruleResult.NodeResults {
				if ruleResult.NodeResults[i].NodeName == nodeName {
					nodeResult = &ruleResult.NodeResults[i]
					break
				}
			}

			// 如果节点结果不存在，创建新的
			if nodeResult == nil {
				ruleResult.NodeResults = append(ruleResult.NodeResults, complianceapi.NodeResult{
					NodeName: nodeName,
					Status:   status,
					Message:  message,
					Evidence: evidence,
				})
			} else {
				// 更新现有节点结果
				nodeResult.Status = status
				nodeResult.Message = message
				nodeResult.Evidence = evidence
			}

			// 更新规则的整体状态（如果任一节点失败，则规则失败）
			if status == complianceapi.CheckResultStatusFail {
				ruleResult.Status = complianceapi.CheckResultStatusFail
				ruleResult.Message = fmt.Sprintf("Failed on node: %s", nodeName)
			}
		}
	}
}

// AggregatedResult contains aggregated scan results
type AggregatedResult struct {
	Result  string
	Message string
	Stats   complianceapi.ScanStats
}

// calculateAggregatedResult calculates the overall scan result based on CheckResults
func (r *ScanReconciler) calculateAggregatedResult(ctx context.Context, scan *complianceapi.Scan, scanID string) (*AggregatedResult, error) {
	// Get all CheckResults for this scan
	var checkResults complianceapi.CheckResultList
	if err := r.List(ctx, &checkResults, client.InNamespace(scan.Namespace),
		client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
		return nil, err
	}

	r.Log.Info("Calculating aggregated result", "scan", scan.Name, "totalCheckResults", len(checkResults.Items))

	// Initialize statistics
	stats := complianceapi.ScanStats{}

	// Default result is COMPLIANT
	result := "COMPLIANT"
	message := "All checks passed"

	// Process each CheckResult
	for _, checkResult := range checkResults.Items {
		// Process rule results in aggregated CheckResult format
		if len(checkResult.Spec.RuleResults) > 0 {
			// Count total rules
			stats.Total += len(checkResult.Spec.RuleResults)

			// Process each rule result
			for _, ruleResult := range checkResult.Spec.RuleResults {
				switch ruleResult.Status {
				case complianceapi.CheckResultStatusPass:
					stats.Pass++
				case complianceapi.CheckResultStatusFail:
					stats.Fail++
					// If any check fails, the overall result is NON-COMPLIANT
					result = "NON-COMPLIANT"
					message = "One or more checks failed"
				case complianceapi.CheckResultStatusError:
					stats.Error++
					// If any check has an error, the overall result is ERROR
					if result != "NON-COMPLIANT" {
						result = "ERROR"
						message = "One or more checks encountered errors"
					}
				case complianceapi.CheckResultStatusManual:
					stats.Manual++
					// If any check requires manual review, the overall result should reflect this
					// MANUAL has lower priority than FAIL and ERROR, but higher than INCONSISTENT
					if result == "COMPLIANT" {
						result = "MANUAL"
						message = "One or more checks require manual verification"
					}
				case complianceapi.CheckResultStatusInconsistent:
					stats.Inconsistent++
					// If any check is inconsistent, the overall result is INCONSISTENT
					if result != "NON-COMPLIANT" && result != "ERROR" {
						result = "INCONSISTENT"
						message = "One or more checks have inconsistent results"
					}
				case complianceapi.CheckResultStatusNotApplicable:
					stats.NotApplicable++
				}
			}
		}
	}

	// If no results found, mark as INCONSISTENT
	if stats.Total == 0 {
		result = "INCONSISTENT"
		message = "No check results found"
	}

	r.Log.Info("Aggregated result calculated",
		"scan", scan.Name,
		"result", result,
		"total", stats.Total,
		"pass", stats.Pass,
		"fail", stats.Fail,
		"error", stats.Error)

	return &AggregatedResult{
		Result:  result,
		Message: message,
		Stats:   stats,
	}, nil
}

// CheckResultData represents the detailed result from a scan
type CheckResultData struct {
	ExitCode  int
	Output    string
	Timestamp string
}

// getCheckResultFromPod extracts the check result from ConfigMap
func (r *ScanReconciler) getCheckResultFromPod(ctx context.Context, job *batchv1.Job) (*CheckResultData, error) {
	expectedScan := job.Labels["compliance-operator.alauda.io/scan"]
	expectedRule := job.Labels["compliance-operator.alauda.io/rule"]

	r.Log.Info("Looking for ConfigMap for job",
		"job", job.Name,
		"expectedScan", expectedScan,
		"expectedRule", expectedRule)

	var resultConfigMap *corev1.ConfigMap

	// Strategy 1: Direct lookup by job name (most efficient)
	directConfigMap := &corev1.ConfigMap{}
	err := r.Get(ctx, client.ObjectKey{
		Name:      job.Name,
		Namespace: job.Namespace,
	}, directConfigMap)

	if err == nil {
		// Found ConfigMap with exact job name, verify it belongs to this job
		if r.verifyConfigMapMatch(directConfigMap, job, expectedScan, expectedRule) {
			resultConfigMap = directConfigMap
			r.Log.Info("Found ConfigMap by direct lookup", "configMap", directConfigMap.Name, "job", job.Name)
		} else {
			r.Log.Info("ConfigMap found by direct lookup but verification failed", "configMap", directConfigMap.Name, "job", job.Name)
		}
	} else if !errors.IsNotFound(err) {
		// Real error, not just "not found"
		return nil, fmt.Errorf("failed to get ConfigMap by direct lookup: %v", err)
	}

	// Strategy 2: If direct lookup failed, try label-based search
	if resultConfigMap == nil {
		r.Log.Info("Direct lookup failed, trying label-based search", "job", job.Name)

		labelSelector := client.MatchingLabels{
			"compliance-operator.alauda.io/scan": expectedScan,
			"compliance-operator.alauda.io/rule": expectedRule,
			"compliance-operator.alauda.io/job":  job.Name,
		}

		var configMaps corev1.ConfigMapList
		if err := r.List(ctx, &configMaps, client.InNamespace(job.Namespace), labelSelector); err != nil {
			return nil, fmt.Errorf("failed to list ConfigMaps by labels: %v", err)
		}

		r.Log.Info("Label-based search results", "job", job.Name, "foundConfigMaps", len(configMaps.Items))

		// Find the best match
		for _, cm := range configMaps.Items {
			if r.verifyConfigMapMatch(&cm, job, expectedScan, expectedRule) {
				resultConfigMap = &cm
				r.Log.Info("Found ConfigMap by label-based search", "configMap", cm.Name, "job", job.Name)
				break
			}
		}
	}

	if resultConfigMap == nil {
		return nil, fmt.Errorf("no result ConfigMap found for job %s (scan: %s, rule: %s)",
			job.Name, expectedScan, expectedRule)
	}

	return r.extractCheckResultData(resultConfigMap, job)
}

// verifyConfigMapMatch verifies that a ConfigMap belongs to the specified job
func (r *ScanReconciler) verifyConfigMapMatch(cm *corev1.ConfigMap, job *batchv1.Job, expectedScan, expectedRule string) bool {
	// Check labels
	if cm.Labels == nil {
		r.Log.V(1).Info("ConfigMap has no labels", "configMap", cm.Name)
		return false
	}

	cmScan := cm.Labels["compliance-operator.alauda.io/scan"]
	cmRule := cm.Labels["compliance-operator.alauda.io/rule"]
	cmJob := cm.Labels["compliance-operator.alauda.io/job"]

	// Check data fields
	scanName := cm.Data["scan_name"]
	ruleId := cm.Data["rule_id"]
	jobName := cm.Data["job_name"]

	r.Log.V(1).Info("ConfigMap verification details",
		"configMap", cm.Name,
		"labelScan", cmScan,
		"labelRule", cmRule,
		"labelJob", cmJob,
		"dataScan", scanName,
		"dataRule", ruleId,
		"dataJob", jobName,
		"expectedScan", expectedScan,
		"expectedRule", expectedRule,
		"expectedJob", job.Name)

	// Verify all fields match
	return cmScan == expectedScan &&
		cmRule == expectedRule &&
		cmJob == job.Name &&
		scanName == expectedScan &&
		ruleId == expectedRule &&
		jobName == job.Name
}

// extractCheckResultData extracts CheckResultData from a ConfigMap
func (r *ScanReconciler) extractCheckResultData(configMap *corev1.ConfigMap, job *batchv1.Job) (*CheckResultData, error) {
	// Extract the exit code from the ConfigMap
	exitCodeStr, exists := configMap.Data["exit_code"]
	if !exists {
		return nil, fmt.Errorf("exit_code not found in ConfigMap %s", configMap.Name)
	}

	// Convert to integer with better error handling
	exitCode := 0
	if exitCodeStr == "0" {
		exitCode = 0
	} else if exitCodeStr == "1" {
		exitCode = 1
	} else if exitCodeStr == "2" {
		exitCode = 2
	} else {
		// Try to parse as integer for other values
		if parsed, err := strconv.Atoi(exitCodeStr); err == nil {
			exitCode = parsed
			// Keep the original exit code for proper status mapping
		} else {
			r.Log.Error(err, "Invalid exit_code format in ConfigMap",
				"configMap", configMap.Name,
				"exitCode", exitCodeStr)
			exitCode = 1 // Default to failure for invalid format
		}
	}

	// Extract additional data
	output := configMap.Data["output"]
	outputEncoding := configMap.Data["output_encoding"]

	// Decode base64 output if specified
	if outputEncoding == "base64" && output != "" {
		if decodedBytes, err := base64.StdEncoding.DecodeString(output); err == nil {
			output = string(decodedBytes)
			r.Log.V(1).Info("Successfully decoded base64 output",
				"originalLength", len(configMap.Data["output"]),
				"decodedLength", len(output))
		} else {
			r.Log.Error(err, "Failed to decode base64 output, using original",
				"configMap", configMap.Name)
		}
	}
	timestamp := configMap.Data["timestamp"]
	checkType := configMap.Data["check_type"]

	// Validate data consistency
	if scanName, exists := configMap.Data["scan_name"]; exists {
		if expectedScan := job.Labels["compliance-operator.alauda.io/scan"]; scanName != expectedScan {
			r.Log.Info("Scan name mismatch in ConfigMap",
				"configMap", configMap.Name,
				"expected", expectedScan,
				"actual", scanName)
		}
	}

	result := &CheckResultData{
		ExitCode:  exitCode,
		Output:    output,
		Timestamp: timestamp,
	}

	r.Log.Info("Successfully extracted check result from ConfigMap",
		"job", job.Name,
		"configMap", configMap.Name,
		"exitCode", exitCode,
		"checkType", checkType,
		"outputLength", len(output))

	return result, nil
}

// buildCheckResultLabels creates comprehensive labels for CheckResult objects
func (r *ScanReconciler) buildCheckResultLabels(scan *complianceapi.Scan, job *batchv1.Job) map[string]string {
	labels := map[string]string{
		// 扫描标识
		"compliance-operator.alauda.io/scan":      scan.Name,
		"compliance-operator.alauda.io/rule":      job.Labels["compliance-operator.alauda.io/rule"],
		"compliance-operator.alauda.io/scan-type": job.Labels["compliance-operator.alauda.io/scan-type"],

		// 规则分类 (从 scan 的 profile 获取)
		"compliance-operator.alauda.io/profile": scan.Spec.Profile,

		// 目标标识
		"compliance-operator.alauda.io/target-type": job.Labels["compliance-operator.alauda.io/scan-type"], // node/platform
	}

	// 添加节点相关标签（如果是节点扫描）
	if nodeName, exists := job.Labels["compliance-operator.alauda.io/node"]; exists {
		labels["compliance-operator.alauda.io/node"] = nodeName
		labels["compliance-operator.alauda.io/node-role"] = r.getNodeRole(nodeName)
	}

	return labels
}

// getNodeRole determines the role of a node (simplified version)
func (r *ScanReconciler) getNodeRole(nodeName string) string {
	// 简化实现：根据节点名称推断角色
	// 在实际实现中，应该查询节点的标签来确定角色
	if strings.Contains(nodeName, "master") || strings.Contains(nodeName, "control") {
		return "master"
	}
	return "worker"
}

// extractCheckMessage extracts a meaningful check message from the output
func (r *ScanReconciler) extractCheckMessage(output, status string) string {
	if output == "" {
		return fmt.Sprintf("Check %s", status)
	}

	lines := strings.Split(output, "\n")

	// Look for lines that start with PASS:, FAIL:, ERROR:, WARNING:, MANUAL:, etc.
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Check for common result patterns
		if strings.HasPrefix(line, "PASS:") ||
			strings.HasPrefix(line, "FAIL:") ||
			strings.HasPrefix(line, "ERROR:") ||
			strings.HasPrefix(line, "WARNING:") ||
			strings.HasPrefix(line, "MANUAL:") {
			return line
		}

		// Also check for lines that contain WARNING (for manual analysis)
		if strings.Contains(line, "WARNING") && status == "manual" {
			return line
		}
	}

	// If no specific pattern found, look for the last non-empty line
	// which is often the final result
	for i := len(lines) - 1; i >= 0; i-- {
		line := strings.TrimSpace(lines[i])
		if line != "" {
			// Limit the length to avoid overly long messages
			if len(line) > 200 {
				line = line[:200] + "..."
			}
			return line
		}
	}

	// Fallback
	return fmt.Sprintf("Check %s", status)
}

// updateScanHistory updates the scan history with the latest result
func (r *ScanReconciler) updateScanHistory(ctx context.Context, scan *complianceapi.Scan, scanID string, checkResultName string, reportName string, stats complianceapi.ScanStats) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Updating scan history")

	// 创建新的历史记录引用
	historyRef := complianceapi.HistoricalResultRef{
		ScanID:          scanID,
		Timestamp:       metav1.Now(),
		CheckResultName: checkResultName,
		ReportName:      reportName,
		Stats:           stats,
	}

	// 获取最新的 Scan 对象
	var latestScan complianceapi.Scan
	if err := r.Get(ctx, types.NamespacedName{Name: scan.Name, Namespace: scan.Namespace}, &latestScan); err != nil {
		return fmt.Errorf("failed to get latest scan: %v", err)
	}

	// 更新最新结果引用
	latestScan.Status.LatestResult = &historyRef

	// 如果 HistoricalResults 字段不存在，初始化它
	if latestScan.Status.HistoricalResults == nil {
		latestScan.Status.HistoricalResults = []complianceapi.HistoricalResultRef{}
	}

	// 添加到历史记录
	latestScan.Status.HistoricalResults = append(latestScan.Status.HistoricalResults, historyRef)

	// 限制历史记录数量
	maxHistoricalResults := 10
	if latestScan.Spec.MaxHistoricalResults > 0 {
		maxHistoricalResults = int(latestScan.Spec.MaxHistoricalResults)
	}

	// 如果历史记录超过限制，裁剪最旧的记录
	if len(latestScan.Status.HistoricalResults) > maxHistoricalResults {
		// 按时间排序，保留最新的记录
		sort.Slice(latestScan.Status.HistoricalResults, func(i, j int) bool {
			return latestScan.Status.HistoricalResults[i].Timestamp.After(latestScan.Status.HistoricalResults[j].Timestamp.Time)
		})

		// 裁剪超出部分
		latestScan.Status.HistoricalResults = latestScan.Status.HistoricalResults[:maxHistoricalResults]
	}

	// 更新 Scan 状态
	if err := r.Status().Update(ctx, &latestScan); err != nil {
		return fmt.Errorf("failed to update scan status with history: %v", err)
	}

	log.Info("Scan history updated successfully")
	return nil
}

// generateScanReport generates an HTML report for the scan
func (r *ScanReconciler) generateScanReport(ctx context.Context, scan *complianceapi.Scan, scanID string, stats complianceapi.ScanStats) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Generating scan report")

	// 使用 scanID 生成唯一的报告名称
	reportName := generateReportConfigMapName(scanID)

	// 获取该扫描的所有 CheckResult
	var checkResults complianceapi.CheckResultList
	if err := r.List(ctx, &checkResults, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return fmt.Errorf("failed to list CheckResults: %v", err)
	}

	if len(checkResults.Items) == 0 {
		return fmt.Errorf("no CheckResults found for scan %s with scanID %s", scan.Name, scanID)
	}

	// 使用第一个 CheckResult 作为主要结果
	mainResult := checkResults.Items[0]

	// 生成 HTML 报告
	htmlReport, err := r.generateHTMLReport(ctx, scan, &mainResult, stats)
	if err != nil {
		return fmt.Errorf("failed to generate HTML report: %v", err)
	}

	// 创建或更新报告 ConfigMap
	reportConfigMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      reportName,
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/resource-type": "report",
			},
		},
		Data: map[string]string{
			"report.html": htmlReport,
		},
	}

	// 设置所有者引用
	if err := controllerutil.SetControllerReference(scan, reportConfigMap, r.Scheme); err != nil {
		return fmt.Errorf("failed to set controller reference for report ConfigMap: %v", err)
	}

	// 创建或更新 ConfigMap
	var existingConfigMap corev1.ConfigMap
	err = r.Get(ctx, types.NamespacedName{Name: reportName, Namespace: scan.Namespace}, &existingConfigMap)
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的 ConfigMap
			if err := r.Create(ctx, reportConfigMap); err != nil {
				return fmt.Errorf("failed to create report ConfigMap: %v", err)
			}
			log.Info("Created scan report ConfigMap", "configMap", reportName)
		} else {
			return fmt.Errorf("failed to check existing report ConfigMap: %v", err)
		}
	} else {
		// 更新现有 ConfigMap
		existingConfigMap.Data = reportConfigMap.Data
		existingConfigMap.Labels = reportConfigMap.Labels
		if err := r.Update(ctx, &existingConfigMap); err != nil {
			return fmt.Errorf("failed to update report ConfigMap: %v", err)
		}
		log.Info("Updated scan report ConfigMap", "configMap", reportName)
	}

	return nil
}

// cleanupCompletedScanResources cleans up temporary resources after a scan
func (r *ScanReconciler) cleanupCompletedScanResources(ctx context.Context, scan *complianceapi.Scan, scanID string, processedConfigMaps map[string]bool) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Cleaning up temporary resources")

	// 获取所有带有指定 scanID 和 temporary=true 标签的 ConfigMap
	var configMaps corev1.ConfigMapList
	if err := r.List(ctx, &configMaps, client.InNamespace(scan.Namespace),
		client.MatchingLabels{
			"compliance-operator.alauda.io/scan-id":   scanID,
			"compliance-operator.alauda.io/temporary": "true",
		}); err != nil {
		return err
	}

	// 只清理已处理的 ConfigMap
	for _, cm := range configMaps.Items {
		if processedConfigMaps[cm.Name] {
			if err := r.Delete(ctx, &cm); err != nil {
				if !errors.IsNotFound(err) {
					log.Error(err, "Failed to delete ConfigMap", "configMap", cm.Name)
				}
			} else {
				log.Info("Successfully deleted ConfigMap", "configMap", cm.Name)
			}
		}
	}

	return nil
}

// processOpenSCAPResults processes OpenSCAP scan results and handles large HTML reports
func (r *ScanReconciler) processOpenSCAPResults(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Processing OpenSCAP scan results")

	// Find completed OpenSCAP jobs
	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
		"compliance-operator.alauda.io/scanner": "openscap",
	}); err != nil {
		return fmt.Errorf("failed to list OpenSCAP jobs: %v", err)
	}

	if len(jobs.Items) == 0 {
		return fmt.Errorf("no OpenSCAP jobs found for scan %s with scanID %s", scan.Name, scanID)
	}

	// Process results from each job
	var allRuleResults []complianceapi.RuleResult
	var htmlReports []OpenSCAPReport

	for _, job := range jobs.Items {
		// Check if job is complete
		if job.Status.Succeeded == 0 {
			continue // Job not yet complete
		}

		// Extract results from job's ConfigMap or Pod logs
		ruleResults, htmlReport, err := r.extractOpenSCAPResults(ctx, &job, scanID)
		if err != nil {
			log.Error(err, "Failed to extract results from OpenSCAP job", "job", job.Name)
			continue
		}

		allRuleResults = append(allRuleResults, ruleResults...)
		if htmlReport != nil {
			htmlReports = append(htmlReports, *htmlReport)
		}
	}

	if len(allRuleResults) == 0 {
		return fmt.Errorf("no valid results found from OpenSCAP jobs")
	}

	// Create aggregated CheckResult
	checkResult := &complianceapi.CheckResult{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%s-%s", scan.Name, scanID),
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/scanner":       "openscap",
				"compliance-operator.alauda.io/resource-type": "check-result",
			},
		},
		Spec: complianceapi.CheckResultSpec{
			ScanName:    scan.Name,
			ProfileName: scan.Spec.Profile,
			RuleResults: allRuleResults,
			Timestamp:   metav1.Now(),
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, checkResult, r.Scheme); err != nil {
		return fmt.Errorf("failed to set owner reference: %v", err)
	}

	// Create CheckResult
	if err := r.Create(ctx, checkResult); err != nil {
		return fmt.Errorf("failed to create CheckResult: %v", err)
	}

	log.Info("Created OpenSCAP CheckResult", "checkResult", checkResult.Name, "ruleCount", len(allRuleResults))

	// Handle HTML reports storage
	if len(htmlReports) > 0 {
		if err := r.storeOpenSCAPHTMLReports(ctx, scan, scanID, htmlReports); err != nil {
			log.Error(err, "Failed to store HTML reports, continuing with other processing")
		}
	}

	// Calculate statistics
	stats := r.calculateStatsFromResults(allRuleResults)

	// Update scan status
	scan.Status.LatestResult = &complianceapi.HistoricalResultRef{
		ScanID:          scanID,
		CheckResultName: checkResult.Name,
		Timestamp:       metav1.Now(),
		Stats:           stats,
	}

	return nil
}

// OpenSCAPReport represents an OpenSCAP HTML report
type OpenSCAPReport struct {
	NodeName    string
	ScanType    string
	HTMLContent string
	Size        int64
}

// extractOpenSCAPResults extracts scan results from OpenSCAP job
func (r *ScanReconciler) extractOpenSCAPResults(ctx context.Context, job *batchv1.Job, scanID string) ([]complianceapi.RuleResult, *OpenSCAPReport, error) {
	// For now, return mock results - this would be implemented to:
	// 1. Get the pod from the job
	// 2. Copy files from the pod's /reports directory
	// 3. Parse XML results to extract rule results
	// 4. Read HTML report content

	log := r.Log.WithValues("job", job.Name, "namespace", job.Namespace)
	log.Info("Extracting OpenSCAP results (mock implementation)")

	// Mock rule results - in real implementation, parse XML results
	ruleResults := []complianceapi.RuleResult{
		{
			RuleID:    "openscap-mock-rule",
			RuleName:  "openscap-mock-rule",
			Severity:  "medium",
			CheckType: "platform",
			Status:    "PASS",
			Message:   "OpenSCAP scan completed successfully",
		},
	}

	// Mock HTML report - in real implementation, read from pod
	htmlReport := &OpenSCAPReport{
		NodeName:    job.Labels["compliance-operator.alauda.io/node"],
		ScanType:    job.Labels["compliance-operator.alauda.io/scan-type"],
		HTMLContent: "<html><body>OpenSCAP Report Placeholder</body></html>",
		Size:        2500000, // 2.5MB to simulate large report
	}

	return ruleResults, htmlReport, nil
}

// storeOpenSCAPHTMLReports handles storage of large HTML reports
func (r *ScanReconciler) storeOpenSCAPHTMLReports(ctx context.Context, scan *complianceapi.Scan, scanID string, reports []OpenSCAPReport) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)

	// Check total size of all reports
	totalSize := int64(0)
	for _, report := range reports {
		totalSize += report.Size
	}

	log.Info("Storing OpenSCAP HTML reports", "reportCount", len(reports), "totalSize", totalSize)

	// ConfigMap size limit is ~1MB, if reports are larger, use alternative storage
	const configMapSizeLimit = 1000000 // 1MB

	if totalSize > configMapSizeLimit {
		log.Info("Reports too large for ConfigMap, using PVC storage", "totalSize", totalSize, "limit", configMapSizeLimit)
		return r.storeReportsInPVC(ctx, scan, scanID, reports)
	} else {
		log.Info("Reports fit in ConfigMap, using ConfigMap storage", "totalSize", totalSize)
		return r.storeReportsInConfigMap(ctx, scan, scanID, reports)
	}
}

// storeReportsInPVC stores large HTML reports in a PersistentVolumeClaim
func (r *ScanReconciler) storeReportsInPVC(ctx context.Context, scan *complianceapi.Scan, scanID string, reports []OpenSCAPReport) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Storing reports in PVC")

	// Create a job to store reports in PVC
	_ = generateJobName("openscap-report-store", scan.Name, scanID) // Reserved for future use

	// Create PVC if it doesn't exist
	pvcName := fmt.Sprintf("compliance-reports-%s", scan.Namespace)
	if err := r.ensureReportsPVC(ctx, scan.Namespace, pvcName); err != nil {
		return fmt.Errorf("failed to ensure reports PVC: %v", err)
	}

	// Create ConfigMap with report metadata (not the full HTML content)
	reportMetadata := make(map[string]string)
	for i, report := range reports {
		reportMetadata[fmt.Sprintf("report_%d_metadata", i)] = fmt.Sprintf(
			"node=%s,type=%s,size=%d,path=/reports/%s/%s_%s_%d.html",
			report.NodeName, report.ScanType, report.Size, scanID, scan.Name, report.NodeName, i,
		)
	}

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      generateReportConfigMapName(scanID),
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/scanner":       "openscap",
				"compliance-operator.alauda.io/resource-type": "report-metadata",
				"compliance-operator.alauda.io/storage-type":  "pvc",
			},
		},
		Data: reportMetadata,
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, configMap, r.Scheme); err != nil {
		return fmt.Errorf("failed to set owner reference: %v", err)
	}

	if err := r.Create(ctx, configMap); err != nil {
		return fmt.Errorf("failed to create report metadata ConfigMap: %v", err)
	}

	log.Info("Created report metadata ConfigMap", "configMap", configMap.Name, "pvc", pvcName)

	// TODO: Implement actual report storage job that:
	// 1. Mounts the PVC
	// 2. Copies HTML files from completed scanner pods to PVC
	// 3. Updates the metadata ConfigMap with actual file paths

	return nil
}

// storeReportsInConfigMap stores small HTML reports in ConfigMap
func (r *ScanReconciler) storeReportsInConfigMap(ctx context.Context, scan *complianceapi.Scan, scanID string, reports []OpenSCAPReport) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Storing reports in ConfigMap")

	reportData := make(map[string]string)
	for i, report := range reports {
		key := fmt.Sprintf("report_%s_%s_%d.html", report.ScanType, report.NodeName, i)
		reportData[key] = report.HTMLContent
	}

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      generateReportConfigMapName(scanID),
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/scanner":       "openscap",
				"compliance-operator.alauda.io/resource-type": "report",
				"compliance-operator.alauda.io/storage-type":  "configmap",
			},
		},
		Data: reportData,
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, configMap, r.Scheme); err != nil {
		return fmt.Errorf("failed to set owner reference: %v", err)
	}

	if err := r.Create(ctx, configMap); err != nil {
		return fmt.Errorf("failed to create report ConfigMap: %v", err)
	}

	log.Info("Created report ConfigMap", "configMap", configMap.Name, "reportCount", len(reports))
	return nil
}

// ensureReportsPVC ensures a PVC exists for storing large reports
func (r *ScanReconciler) ensureReportsPVC(ctx context.Context, namespace, pvcName string) error {
	// Check if PVC already exists
	var existingPVC corev1.PersistentVolumeClaim
	err := r.Get(ctx, client.ObjectKey{Name: pvcName, Namespace: namespace}, &existingPVC)
	if err == nil {
		// PVC already exists
		return nil
	}

	if !errors.IsNotFound(err) {
		return fmt.Errorf("failed to check PVC existence: %v", err)
	}

	// Create new PVC
	pvc := &corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      pvcName,
			Namespace: namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/resource-type": "reports-storage",
				"compliance-operator.alauda.io/storage-type":  "pvc",
			},
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteOnce,
			},
			Resources: corev1.ResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: resource.MustParse("1Gi"), // 1GB for reports
				},
			},
		},
	}

	if err := r.Create(ctx, pvc); err != nil {
		return fmt.Errorf("failed to create PVC: %v", err)
	}

	r.Log.Info("Created reports PVC", "pvc", pvcName, "namespace", namespace)
	return nil
}

// calculateStatsFromResults calculates scan statistics from rule results
func (r *ScanReconciler) calculateStatsFromResults(results []complianceapi.RuleResult) complianceapi.ScanStats {
	stats := complianceapi.ScanStats{
		Total: len(results),
	}

	for _, result := range results {
		switch result.Status {
		case "PASS":
			stats.Pass++
		case "FAIL":
			stats.Fail++
		case "ERROR":
			stats.Error++
		case "MANUAL":
			stats.Manual++
		case "NOT-APPLICABLE":
			stats.NotApplicable++
		case "INCONSISTENT":
			stats.Inconsistent++
		}
	}

	return stats
}

// cleanupScanJobs cleans up completed scan jobs for a specific scanID
func (r *ScanReconciler) cleanupScanJobs(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Cleaning up scan jobs")

	// 获取所有与此扫描和scanID相关的Job
	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return fmt.Errorf("failed to list scan jobs: %v", err)
	}

	log.Info("Found scan jobs to cleanup", "jobCount", len(jobs.Items))

	// 删除所有Job（TTL会处理Pod的清理）
	for _, job := range jobs.Items {
		// 只清理已完成的Job
		if job.Status.Succeeded > 0 || job.Status.Failed > 0 {
			if err := r.Delete(ctx, &job); err != nil {
				if !errors.IsNotFound(err) {
					log.Error(err, "Failed to delete scan job", "job", job.Name)
					// 继续清理其他Job，不因为单个Job清理失败而中断
				}
			} else {
				log.Info("Successfully deleted scan job", "job", job.Name)
			}
		}
	}

	return nil
}
